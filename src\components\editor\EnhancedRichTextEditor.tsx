import React, { useEffect, useState, useCallback } from 'react';
import { useEditor, EditorContent, BubbleMenu, FloatingMenu } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Typography from '@tiptap/extension-typography';
import Underline from '@tiptap/extension-underline';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
// Removed FontFamily import
import Highlight from '@tiptap/extension-highlight';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import VariableExtension from './VariableExtension';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Heading1,
  Heading2,
  Heading3,
  Type,
  Code,
  Quote,
  Undo,
  Redo,
  Variable as VariableIcon,
  Table as TableIcon,
  Image as ImageIcon,
  Link as LinkIcon,
  Unlink,
  Indent,
  Outdent,
  Trash2,
  Palette,
  Highlighter,
  Subscript as SubscriptIcon,
  Superscript as SuperscriptIcon,
  TextCursorInput,
  SquareEqual,
  RowsIcon,
  ColumnsIcon,
  Trash,
  Code2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger
} from '@/components/ui/context-menu';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/core';
import { toast } from 'sonner';
import { Variable } from '@/core/utils/contract-utils';
import './EditorStyles.css';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  variables?: Variable[];
  onInsertVariable?: (variable: Variable) => void;
  className?: string;
  editorClassName?: string;
  readOnly?: boolean;
}

const EnhancedRichTextEditor = ({
  content,
  onChange,
  placeholder = 'Start typing...',
  variables = [],
  onInsertVariable,
  className,
  editorClassName,
  readOnly = false
}: RichTextEditorProps) => {
  const [isMounted, setIsMounted] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const [linkDialogOpen, setLinkDialogOpen] = useState(false);
  const [showHtmlEditor, setShowHtmlEditor] = useState(false);
  const [htmlContent, setHtmlContent] = useState('');
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | null>(null);
  const [contextMenuType, setContextMenuType] = useState<'text' | 'table' | 'general'>('general');

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
        paragraph: {
          HTMLAttributes: {
            class: 'whitespace-pre-wrap',
          },
        },
        hardBreak: {
          keepMarks: true,
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      Typography,
      Underline,
      TextStyle,
      Color,
      Highlight.configure({ multicolor: true }),
      Subscript,
      Superscript,
      Table.configure({
        resizable: true,
        allowTableNodeSelection: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Image,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 underline',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph', 'blockquote'],
      }),
      VariableExtension.configure({
        HTMLAttributes: {
          class: 'variable',
        },
      }),
    ],
    content,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange(html);
      setHtmlContent(html);
    },
  });



  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (editor && isMounted) {
      try {
        const currentEditorContent = editor.getHTML();

        // Log the content lengths for debugging
        (`Content prop length: ${content.length}, Editor content length: ${currentEditorContent.length}`);

        // Always update the editor content when the prop changes
        // This ensures the editor always reflects the latest content from the parent
        if (content !== currentEditorContent) {
          ('Content prop changed, updating editor');

          // Store the content in both states to ensure consistency
          editor.commands.setContent(content);
          setHtmlContent(content);

          // Save to localStorage as a backup
          try {
            localStorage.setItem('editor_content_backup', content);
          } catch (e) {
            console.error('Failed to save backup to localStorage:', e);
          }
        }
      } catch (error) {
        console.error('Error updating editor content:', error);
        toast.error('There was an error updating the editor content. Please try again.');
      }
    }
  }, [editor, isMounted, content]);

  // Update editor content when HTML is edited directly or when switching modes
  useEffect(() => {
    if (editor) {
      try {
        // When in HTML mode, we don't need to update the editor immediately
        // as it will be updated when switching back to visual mode
        // But we do need to ensure the editor has the latest content when not in HTML mode
        if (!showHtmlEditor && htmlContent !== editor.getHTML()) {
          ('HTML content changed, updating editor');
          editor.commands.setContent(htmlContent);
        }
      } catch (error) {
        console.error('Error syncing HTML content with editor:', error);
      }
    }
  }, [showHtmlEditor, htmlContent, editor]);

  if (!isMounted) {
    return null;
  }

  const variablesByCategory = variables.reduce((acc, variable) => {
    if (!acc[variable.category]) {
      acc[variable.category] = [];
    }
    acc[variable.category].push(variable);
    return acc;
  }, {} as Record<string, Variable[]>);

  const categoryLabels = {
    booking: 'Booking',
    artist: 'Artist',
    venue: 'Venue',
    other: 'Other'
  };

  const handleSetLink = () => {
    if (editor && linkUrl) {
      editor.chain().focus().setLink({ href: linkUrl }).run();
      setLinkUrl('');
      setLinkDialogOpen(false);
    }
  };

  const handleInsertImage = () => {
    const url = prompt('Enter image URL');
    if (url && editor) {
      editor.chain().focus().setImage({ src: url }).run();
    }
  };

  const handleInsertTable = () => {
    if (editor) {
      editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
    }
  };

  const handleAddTableRow = () => {
    if (editor) {
      editor.chain().focus().addRowAfter().run();
    }
  };

  const handleAddTableColumn = () => {
    if (editor) {
      editor.chain().focus().addColumnAfter().run();
    }
  };

  const handleDeleteTableRow = () => {
    if (editor) {
      editor.chain().focus().deleteRow().run();
    }
  };

  const handleDeleteTableColumn = () => {
    if (editor) {
      editor.chain().focus().deleteColumn().run();
    }
  };

  const handleDeleteTable = () => {
    if (editor) {
      editor.chain().focus().deleteTable().run();
    }
  };

  const handleMergeCells = () => {
    if (editor) {
      editor.chain().focus().mergeCells().run();
    }
  };

  const handleSplitCell = () => {
    if (editor) {
      editor.chain().focus().splitCell().run();
    }
  };

  const handleToggleHtmlEditor = () => {
    // Before toggling, ensure content is synchronized
    if (showHtmlEditor) {
      // Switching from HTML to Visual - update the editor content
      if (editor) {
        try {
          // Save current cursor position (for potential future use)
          // const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
          // const cursorPos = textarea?.selectionStart || 0;

          // Update editor content
          editor.commands.setContent(htmlContent);

          // Notify parent about the change
          onChange(htmlContent);

          console.log('Switched to Visual Editor with content:', htmlContent.substring(0, 100) + '...');
        } catch (error) {
          console.error('Error switching to visual editor:', error);
          toast.error('There was an error switching to the visual editor. Your content has been preserved in HTML mode.');
          return; // Don't switch if there's an error
        }
      }
    } else {
      // Switching from Visual to HTML - update the HTML content
      if (editor) {
        try {
          const html = editor.getHTML();
          setHtmlContent(html);
          console.log('Switched to HTML Editor with content:', html.substring(0, 100) + '...');
        } catch (error) {
          console.error('Error switching to HTML editor:', error);
          toast.error('There was an error switching to the HTML editor. Your content has been preserved in visual mode.');
          return; // Don't switch if there's an error
        }
      }
    }

    // Toggle the editor mode
    setShowHtmlEditor(!showHtmlEditor);
  };

  const handleHtmlContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (!isMounted) return;

    const newContent = e.target.value;
    setHtmlContent(newContent);

    // Immediately notify parent component of the change
    // This ensures the parent always has the latest content
    onChange(newContent);

    // Also save to localStorage as a backup
    try {
      localStorage.setItem('editor_content_backup', newContent);
    } catch (e) {
      console.error('Failed to save backup to localStorage:', e);
    }
  };

  const handleInsertVariable = (variable: Variable) => {
    if (!editor) return;

    // Insert the variable based on the current mode
    if (showHtmlEditor) {
      // If in HTML mode, insert the HTML code at cursor position
      const variableHtml = `<span class="variable" data-variable-id="${variable.id}" data-variable-name="${variable.name}">${variable.label}</span>`;

      const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
      if (textarea) {
        const cursorPos = textarea.selectionStart;
        const textBefore = htmlContent.substring(0, cursorPos);
        const textAfter = htmlContent.substring(cursorPos);
        const newHtml = textBefore + variableHtml + textAfter;
        setHtmlContent(newHtml);

        // Set cursor position after the inserted variable and show a toast notification
        setTimeout(() => {
          textarea.focus();
          textarea.setSelectionRange(cursorPos + variableHtml.length, cursorPos + variableHtml.length);
          toast.success(`Variable '${variable.label}' inserted`);
        }, 0);
      }
    } else {
      // In visual editor mode, use our custom variable extension
      editor.chain().focus().insertVariable({
        id: variable.id,
        name: variable.name,
        label: variable.label
      }).run();

      toast.success(`Variable '${variable.label}' inserted`);
    }

    // Notify parent component about the variable insertion
    if (onInsertVariable) {
      onInsertVariable(variable);
    }
  };

  // Context menu handlers
  const handleContextMenu = (event: React.MouseEvent) => {
    if (readOnly || !editor) return;

    event.preventDefault();

    // Determine context menu type based on current selection/position
    const { state } = editor;
    const { selection } = state;

    let menuType: 'text' | 'table' | 'general' = 'general';

    if (editor.isActive('table')) {
      menuType = 'table';
    } else if (!selection.empty) {
      menuType = 'text';
    }

    setContextMenuType(menuType);
    setContextMenuPosition({ x: event.clientX, y: event.clientY });
  };

  const handleCloseContextMenu = () => {
    setContextMenuPosition(null);
  };

  // Text formatting context menu actions
  const handleContextBold = () => {
    if (editor) {
      editor.chain().focus().toggleBold().run();
      handleCloseContextMenu();
    }
  };

  const handleContextItalic = () => {
    if (editor) {
      editor.chain().focus().toggleItalic().run();
      handleCloseContextMenu();
    }
  };

  const handleContextUnderline = () => {
    if (editor) {
      editor.chain().focus().toggleUnderline().run();
      handleCloseContextMenu();
    }
  };

  // Table context menu actions
  const handleContextAddRowAbove = () => {
    if (editor) {
      editor.chain().focus().addRowBefore().run();
      handleCloseContextMenu();
    }
  };

  const handleContextAddRowBelow = () => {
    if (editor) {
      editor.chain().focus().addRowAfter().run();
      handleCloseContextMenu();
    }
  };

  const handleContextAddColumnLeft = () => {
    if (editor) {
      editor.chain().focus().addColumnBefore().run();
      handleCloseContextMenu();
    }
  };

  const handleContextAddColumnRight = () => {
    if (editor) {
      editor.chain().focus().addColumnAfter().run();
      handleCloseContextMenu();
    }
  };

  const handleContextDeleteRow = () => {
    if (editor) {
      editor.chain().focus().deleteRow().run();
      handleCloseContextMenu();
    }
  };

  const handleContextDeleteColumn = () => {
    if (editor) {
      editor.chain().focus().deleteColumn().run();
      handleCloseContextMenu();
    }
  };

  const handleContextMergeCells = () => {
    if (editor) {
      editor.chain().focus().mergeCells().run();
      handleCloseContextMenu();
    }
  };

  const handleContextSplitCell = () => {
    if (editor) {
      editor.chain().focus().splitCell().run();
      handleCloseContextMenu();
    }
  };

  const handleContextDeleteTable = () => {
    if (editor) {
      editor.chain().focus().deleteTable().run();
      handleCloseContextMenu();
    }
  };

  // Additional context menu actions
  const handleContextCopy = async () => {
    if (!editor) return;
    try {
      await navigator.clipboard.writeText(editor.state.doc.textBetween(
        editor.state.selection.from,
        editor.state.selection.to,
        ' '
      ) || '');
      toast.success('Text copied to clipboard');
      handleCloseContextMenu();
    } catch (err) {
      toast.error('Failed to copy text');
      handleCloseContextMenu();
    }
  };

  const handleContextCut = async () => {
    if (!editor) return;
    try {
      const selectedText = editor.state.doc.textBetween(
        editor.state.selection.from,
        editor.state.selection.to,
        ' '
      ) || '';
      await navigator.clipboard.writeText(selectedText);
      editor.chain().focus().deleteSelection().run();
      toast.success('Text cut to clipboard');
      handleCloseContextMenu();
    } catch (err) {
      toast.error('Failed to cut text');
      handleCloseContextMenu();
    }
  };

  const handleContextPaste = async () => {
    if (!editor) return;
    try {
      const text = await navigator.clipboard.readText();
      editor.chain().focus().insertContent(text).run();
      toast.success('Text pasted');
      handleCloseContextMenu();
    } catch (err) {
      toast.error('Failed to paste text');
      handleCloseContextMenu();
    }
  };

  const handleContextHighlight = (color: string) => {
    if (editor) {
      editor.chain().focus().toggleHighlight({ color }).run();
      handleCloseContextMenu();
    }
  };

  const handleContextTextColor = (color: string) => {
    if (editor) {
      editor.chain().focus().setColor(color).run();
      handleCloseContextMenu();
    }
  };


  return (
    <div className={cn("rich-text-editor border rounded-md flex flex-col", className)}>
      {!readOnly && editor && (
        <div className="flex flex-wrap gap-1 p-3 border-b bg-muted/20">
          {/* Variables - Most Important */}
          {variables.length > 0 && (
            <>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="default" className="flex items-center gap-2 bg-gray-900" type="button" title="Insert Variable">
                    <VariableIcon className="h-4 w-4" />
                    <span className="font-medium">Variables</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-96" align="start">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-lg">Insert Variable</h4>
                      <p className="text-sm text-muted-foreground">
                        Variables will be replaced with actual data when the document is generated.
                      </p>
                    </div>
                    <ScrollArea className="h-72 border rounded p-2">
                      <div className="space-y-4 pr-3">
                        {Object.entries(variablesByCategory).map(([category, vars]) => (
                          <div key={category} className="space-y-2">
                            <h5 className="text-sm font-medium bg-muted px-2 py-1 rounded">{categoryLabels[category as keyof typeof categoryLabels]}</h5>
                            <div className="grid grid-cols-1 gap-1 pl-2">
                              {vars.map((variable) => (
                                <Button
                                  key={variable.id}
                                  variant="ghost"
                                  size="sm"
                                  className="justify-start text-left font-normal hover:bg-blue-50 hover:text-blue-700"
                                  onClick={() => handleInsertVariable(variable)}
                                  type="button"
                                >
                                  <span className="truncate">{variable.label}</span>
                                  {variable.description && (
                                    <span className="ml-2 text-xs text-muted-foreground hidden group-hover:inline-block">{variable.description}</span>
                                  )}
                                </Button>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </PopoverContent>
              </Popover>
              <div className="w-px h-8 bg-border mx-2" />
            </>
          )}

          {/* Text Style */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2 px-3"
                type="button"
                title="Text Style"
              >
                <Type className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {editor.isActive('heading', { level: 1 }) && 'Heading 1'}
                  {editor.isActive('heading', { level: 2 }) && 'Heading 2'}
                  {editor.isActive('heading', { level: 3 }) && 'Heading 3'}
                  {editor.isActive('paragraph') && 'Normal'}
                  {!editor.isActive('heading') && !editor.isActive('paragraph') && 'Style'}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().setParagraph().run()}
                className={editor.isActive('paragraph') ? 'bg-muted' : ''}
              >
                <span className="font-normal">Normal text</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                className={editor.isActive('heading', { level: 1 }) ? 'bg-muted' : ''}
              >
                <span className="text-2xl font-bold">Heading 1</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                className={editor.isActive('heading', { level: 2 }) ? 'bg-muted' : ''}
              >
                <span className="text-xl font-semibold">Heading 2</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                className={editor.isActive('heading', { level: 3 }) ? 'bg-muted' : ''}
              >
                <span className="text-lg font-medium">Heading 3</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Text formatting */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={editor.isActive('bold') ? 'bg-muted' : ''}
              type="button"
              title="Bold (Ctrl+B)"
            >
              <Bold className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={editor.isActive('italic') ? 'bg-muted' : ''}
              type="button"
              title="Italic (Ctrl+I)"
            >
              <Italic className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              className={editor.isActive('underline') ? 'bg-muted' : ''}
              type="button"
              title="Underline (Ctrl+U)"
            >
              <UnderlineIcon className="h-4 w-4" />
            </Button>
          </div>

          <div className="w-px h-8 bg-border mx-2" />

          {/* Text alignment - More prominent */}
          <div className="flex items-center gap-1 bg-muted/30 rounded-md p-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().setTextAlign('left').run()}
              className={cn("h-7 w-7", editor.isActive({ textAlign: 'left' }) ? 'bg-primary text-primary-foreground' : '')}
              type="button"
              title="Align Left"
            >
              <AlignLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().setTextAlign('center').run()}
              className={cn("h-7 w-7", editor.isActive({ textAlign: 'center' }) ? 'bg-primary text-primary-foreground' : '')}
              type="button"
              title="Align Center"
            >
              <AlignCenter className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().setTextAlign('right').run()}
              className={cn("h-7 w-7", editor.isActive({ textAlign: 'right' }) ? 'bg-primary text-primary-foreground' : '')}
              type="button"
              title="Align Right"
            >
              <AlignRight className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().setTextAlign('justify').run()}
              className={cn("h-7 w-7", editor.isActive({ textAlign: 'justify' }) ? 'bg-primary text-primary-foreground' : '')}
              type="button"
              title="Justify"
            >
              <AlignJustify className="h-4 w-4" />
            </Button>
          </div>

          <div className="w-px h-8 bg-border mx-2" />

          {/* Lists and Structure */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className={editor.isActive('bulletList') ? 'bg-muted' : ''}
              type="button"
              title="Bullet List"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              className={editor.isActive('orderedList') ? 'bg-muted' : ''}
              type="button"
              title="Numbered List"
            >
              <ListOrdered className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              className={editor.isActive('blockquote') ? 'bg-muted' : ''}
              type="button"
              title="Quote"
            >
              <Quote className="h-4 w-4" />
            </Button>
          </div>

          <div className="w-px h-8 bg-border mx-2" />

          {/* Table/Grid - Enhanced */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2 px-3"
                type="button"
                title="Insert Table/Grid"
              >
                <TableIcon className="h-4 w-4" />
                <span className="font-medium">Table</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuItem onClick={handleInsertTable}>
                <TableIcon className="h-4 w-4 mr-2" />
                <div className="flex flex-col">
                  <span>Insert Table</span>
                  <span className="text-xs text-muted-foreground">3×3 with headers</span>
                </div>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleAddTableRow} disabled={!editor?.isActive('table')}>
                <RowsIcon className="h-4 w-4 mr-2" /> Add Row Below
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleAddTableColumn} disabled={!editor?.isActive('table')}>
                <ColumnsIcon className="h-4 w-4 mr-2" /> Add Column Right
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleDeleteTableRow} disabled={!editor?.isActive('table')}>
                <Trash className="h-4 w-4 mr-2" /> Delete Row
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDeleteTableColumn} disabled={!editor?.isActive('table')}>
                <Trash className="h-4 w-4 mr-2" /> Delete Column
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleMergeCells} disabled={!editor?.isActive('table')}>
                <SquareEqual className="h-4 w-4 mr-2" /> Merge Cells
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleSplitCell} disabled={!editor?.isActive('table')}>
                <SquareEqual className="h-4 w-4 mr-2" /> Split Cell
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleDeleteTable} disabled={!editor?.isActive('table')} className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" /> Delete Table
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <div className="w-px h-8 bg-border mx-2" />

          {/* Advanced Formatting */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" type="button" title="More Formatting">
                <Palette className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-64">
              <div className="p-3 space-y-3">
                <div>
                  <h4 className="font-medium mb-2">Text Color</h4>
                  <div className="grid grid-cols-6 gap-1">
                    {['#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#808080', '#800000', '#808000', '#008000', '#800080'].map((color) => (
                      <Button
                        key={color}
                        variant="outline"
                        className="w-6 h-6 p-0"
                        style={{ backgroundColor: color }}
                        onClick={() => editor.chain().focus().setColor(color).run()}
                      />
                    ))}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full mt-2"
                    onClick={() => editor.chain().focus().unsetColor().run()}
                  >
                    Reset color
                  </Button>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Highlight</h4>
                  <div className="grid grid-cols-6 gap-1">
                    {['#FFFF00', '#00FFFF', '#FF00FF', '#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#F0F0F0', '#D3D3D3', '#FFC0CB', '#FFD700', '#FFA500'].map((color) => (
                      <Button
                        key={color}
                        variant="outline"
                        className="w-6 h-6 p-0"
                        style={{ backgroundColor: color }}
                        onClick={() => editor.chain().focus().toggleHighlight({ color }).run()}
                      />
                    ))}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full mt-2"
                    onClick={() => editor.chain().focus().unsetHighlight().run()}
                  >
                    Remove highlight
                  </Button>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => editor.chain().focus().toggleSubscript().run()}
                    className={editor.isActive('subscript') ? 'bg-muted' : ''}
                    type="button"
                    title="Subscript"
                  >
                    <SubscriptIcon className="h-4 w-4 mr-1" />
                    Sub
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => editor.chain().focus().toggleSuperscript().run()}
                    className={editor.isActive('superscript') ? 'bg-muted' : ''}
                    type="button"
                    title="Superscript"
                  >
                    <SuperscriptIcon className="h-4 w-4 mr-1" />
                    Super
                  </Button>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          <div className="w-px h-8 bg-border mx-2" />

          {/* Links and Media */}
          <div className="flex items-center gap-1">
            <Popover open={linkDialogOpen} onOpenChange={setLinkDialogOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={editor.isActive('link') ? 'bg-muted' : ''}
                  type="button"
                  title="Insert Link"
                >
                  <LinkIcon className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80" align="start">
                <div className="space-y-2">
                  <h4 className="font-medium">Insert Link</h4>
                  <div className="space-y-2">
                    <Label htmlFor="url">URL</Label>
                    <Input
                      id="url"
                      value={linkUrl}
                      onChange={(e) => setLinkUrl(e.target.value)}
                      placeholder="https://example.com"
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setLinkDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSetLink}
                      disabled={!linkUrl}
                    >
                      Save
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().unsetLink().run()}
              disabled={!editor.isActive('link')}
              type="button"
              title="Remove Link"
            >
              <Unlink className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="icon"
              onClick={handleInsertImage}
              type="button"
              title="Insert Image"
            >
              <ImageIcon className="h-4 w-4" />
            </Button>
          </div>

          <div className="w-px h-8 bg-border mx-2" />

          {/* Utility Actions */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              type="button"
              title="Undo (Ctrl+Z)"
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              type="button"
              title="Redo (Ctrl+Y)"
            >
              <Redo className="h-4 w-4" />
            </Button>
          </div>

          <div className="w-px h-8 bg-border mx-2" />

          {/* HTML Editor Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleHtmlEditor}
            className={cn(
              "flex items-center gap-1",
              showHtmlEditor ? 'bg-amber-100 hover:bg-amber-200 border-amber-300' : 'bg-slate-50 hover:bg-slate-100'
            )}
            type="button"
            title="Toggle HTML Source Editor"
          >
            <Code2 className="h-4 w-4" />
            <span className="text-xs">{showHtmlEditor ? 'Visual' : 'HTML'}</span>
          </Button>
        </div>
      )}

      <div className="flex-grow flex flex-col h-[750px] overflow-hidden">
        {showHtmlEditor ? (
          <div className="p-4 space-y-3 flex flex-col h-full">
            <div className="bg-blue-50 p-3 rounded border border-blue-200 text-sm">
              <p className="font-medium text-blue-800">HTML Source Editor</p>
              <p className="text-blue-700 mt-1">You can edit the HTML directly here. Use the "Insert Variable" button to add variables that will be replaced with actual data when the contract is generated.</p>
            </div>
            <div className="flex-grow relative border rounded overflow-hidden">
              <textarea
                value={htmlContent}
                onChange={handleHtmlContentChange}
                className="absolute inset-0 w-full h-full font-mono text-base p-4 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                placeholder="Edit HTML directly..."
                spellCheck="false"
                wrap="soft"
              />
            </div>
          </div>
        ) : (
          <div className="flex-grow flex flex-col overflow-hidden">
            <ContextMenu>
              <ContextMenuTrigger asChild>
                <div
                  className="flex-grow overflow-hidden"
                  onContextMenu={handleContextMenu}
                >
                  <EditorContent
                    editor={editor}
                    className={cn(
                      "prose prose-base max-w-none focus:outline-none flex-grow overflow-y-auto h-full",
                      "prose-table:border-collapse prose-td:border prose-td:border-gray-300 prose-td:p-2",
                      "prose-th:border prose-th:border-gray-300 prose-th:p-2 prose-th:bg-gray-100",
                      "prose-p:whitespace-pre-wrap prose-li:whitespace-pre-wrap", // Preserve whitespace in paragraphs and list items
                      editorClassName,
                      readOnly && "pointer-events-none opacity-90"
                    )}
                  />
                </div>
              </ContextMenuTrigger>

              <ContextMenuContent className="w-64">
                {/* Text Selection Context Menu */}
                {contextMenuType === 'text' && (
                  <>
                  <ContextMenuItem onClick={handleContextCopy}>
                    <span className="mr-2">📋</span>
                    <span>Copy</span>
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+C</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={handleContextCut}>
                    <span className="mr-2">✂️</span>
                    <span>Cut</span>
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+X</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={handleContextPaste}>
                    <span className="mr-2">📄</span>
                    <span>Paste</span>
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+V</span>
                  </ContextMenuItem>
                  <ContextMenuSeparator />
                  <ContextMenuItem onClick={handleContextBold} disabled={!editor?.can().toggleBold()}>
                    <Bold className="mr-2 h-4 w-4" />
                    <span>Bold</span>
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+B</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={handleContextItalic} disabled={!editor?.can().toggleItalic()}>
                    <Italic className="mr-2 h-4 w-4" />
                    <span>Italic</span>
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+I</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={handleContextUnderline} disabled={!editor?.can().toggleUnderline()}>
                    <UnderlineIcon className="mr-2 h-4 w-4" />
                    <span>Underline</span>
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+U</span>
                  </ContextMenuItem>
                  <ContextMenuSeparator />
                  <ContextMenuSub>
                    <ContextMenuSubTrigger>
                      <Palette className="mr-2 h-4 w-4" />
                      <span>Text Color</span>
                    </ContextMenuSubTrigger>
                    <ContextMenuSubContent>
                      <div className="p-2">
                        <div className="grid grid-cols-4 gap-1 mb-2">
                          {['#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#808080'].map((color) => (
                            <button
                              key={color}
                              className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                              style={{ backgroundColor: color }}
                              onClick={() => handleContextTextColor(color)}
                              title={`Set text color to ${color}`}
                            />
                          ))}
                        </div>
                        <ContextMenuItem onClick={() => editor?.chain().focus().unsetColor().run()}>
                          <span className="text-sm">Reset Color</span>
                        </ContextMenuItem>
                      </div>
                    </ContextMenuSubContent>
                  </ContextMenuSub>
                  <ContextMenuSub>
                    <ContextMenuSubTrigger>
                      <Highlighter className="mr-2 h-4 w-4" />
                      <span>Highlight</span>
                    </ContextMenuSubTrigger>
                    <ContextMenuSubContent>
                      <div className="p-2">
                        <div className="grid grid-cols-4 gap-1 mb-2">
                          {['#FFFF00', '#00FFFF', '#FF00FF', '#FF0000', '#00FF00', '#0000FF', '#FFA500', '#FFD700'].map((color) => (
                            <button
                              key={color}
                              className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                              style={{ backgroundColor: color }}
                              onClick={() => handleContextHighlight(color)}
                              title={`Highlight with ${color}`}
                            />
                          ))}
                        </div>
                        <ContextMenuItem onClick={() => editor?.chain().focus().unsetHighlight().run()}>
                          <span className="text-sm">Remove Highlight</span>
                        </ContextMenuItem>
                      </div>
                    </ContextMenuSubContent>
                  </ContextMenuSub>
                  <ContextMenuSeparator />
                  <ContextMenuSub>
                    <ContextMenuSubTrigger>
                      <AlignLeft className="mr-2 h-4 w-4" />
                      <span>Text Alignment</span>
                    </ContextMenuSubTrigger>
                    <ContextMenuSubContent>
                      <ContextMenuItem onClick={() => editor?.chain().focus().setTextAlign('left').run()}>
                        <AlignLeft className="mr-2 h-4 w-4" />
                        <span>Align Left</span>
                      </ContextMenuItem>
                      <ContextMenuItem onClick={() => editor?.chain().focus().setTextAlign('center').run()}>
                        <AlignCenter className="mr-2 h-4 w-4" />
                        <span>Align Center</span>
                      </ContextMenuItem>
                      <ContextMenuItem onClick={() => editor?.chain().focus().setTextAlign('right').run()}>
                        <AlignRight className="mr-2 h-4 w-4" />
                        <span>Align Right</span>
                      </ContextMenuItem>
                      <ContextMenuItem onClick={() => editor?.chain().focus().setTextAlign('justify').run()}>
                        <AlignJustify className="mr-2 h-4 w-4" />
                        <span>Justify</span>
                      </ContextMenuItem>
                    </ContextMenuSubContent>
                  </ContextMenuSub>
                  <ContextMenuSeparator />
                  <ContextMenuItem onClick={() => editor?.chain().focus().toggleBulletList().run()}>
                    <List className="mr-2 h-4 w-4" />
                    <span>Bullet List</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => editor?.chain().focus().toggleOrderedList().run()}>
                    <ListOrdered className="mr-2 h-4 w-4" />
                    <span>Numbered List</span>
                  </ContextMenuItem>
                  {variables.length > 0 && (
                    <>
                      <ContextMenuSeparator />
                      <ContextMenuSub>
                        <ContextMenuSubTrigger>
                          <VariableIcon className="mr-2 h-4 w-4" />
                          <span>Insert Variable</span>
                        </ContextMenuSubTrigger>
                        <ContextMenuSubContent className="w-64">
                          <ScrollArea className="h-48">
                            {Object.entries(variablesByCategory).map(([category, vars]) => (
                              <div key={category}>
                                <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
                                  {categoryLabels[category as keyof typeof categoryLabels]}
                                </div>
                                {vars.map((variable) => (
                                  <ContextMenuItem
                                    key={variable.id}
                                    onClick={() => {
                                      handleInsertVariable(variable);
                                      handleCloseContextMenu();
                                    }}
                                  >
                                    <span className="truncate">{variable.label}</span>
                                  </ContextMenuItem>
                                ))}
                              </div>
                            ))}
                          </ScrollArea>
                        </ContextMenuSubContent>
                      </ContextMenuSub>
                    </>
                  )}
                  </>
                )}

                {/* Table Context Menu */}
                {contextMenuType === 'table' && (
                  <>
                  <ContextMenuItem onClick={handleContextAddRowAbove}>
                    <RowsIcon className="mr-2 h-4 w-4" />
                    <span>Add Row Above</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={handleContextAddRowBelow}>
                    <RowsIcon className="mr-2 h-4 w-4" />
                    <span>Add Row Below</span>
                  </ContextMenuItem>
                  <ContextMenuSeparator />
                  <ContextMenuItem onClick={handleContextAddColumnLeft}>
                    <ColumnsIcon className="mr-2 h-4 w-4" />
                    <span>Add Column Left</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={handleContextAddColumnRight}>
                    <ColumnsIcon className="mr-2 h-4 w-4" />
                    <span>Add Column Right</span>
                  </ContextMenuItem>
                  <ContextMenuSeparator />
                  <ContextMenuItem onClick={handleContextDeleteRow} disabled={!editor?.can().deleteRow()}>
                    <Trash className="mr-2 h-4 w-4" />
                    <span>Delete Row</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={handleContextDeleteColumn} disabled={!editor?.can().deleteColumn()}>
                    <Trash className="mr-2 h-4 w-4" />
                    <span>Delete Column</span>
                  </ContextMenuItem>
                  <ContextMenuSeparator />
                  <ContextMenuItem onClick={handleContextMergeCells} disabled={!editor?.can().mergeCells()}>
                    <SquareEqual className="mr-2 h-4 w-4" />
                    <span>Merge Cells</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={handleContextSplitCell} disabled={!editor?.can().splitCell()}>
                    <SquareEqual className="mr-2 h-4 w-4" />
                    <span>Split Cell</span>
                  </ContextMenuItem>
                  <ContextMenuSeparator />
                  <ContextMenuItem onClick={handleContextDeleteTable} className="text-red-600">
                    <Trash2 className="mr-2 h-4 w-4" />
                    <span>Delete Table</span>
                  </ContextMenuItem>
                  </>
                )}

                {/* General Context Menu */}
                {contextMenuType === 'general' && (
                  <>
                  <ContextMenuItem onClick={handleContextPaste}>
                    <span className="mr-2">📄</span>
                    <span>Paste</span>
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+V</span>
                  </ContextMenuItem>
                  <ContextMenuSeparator />
                  <ContextMenuItem onClick={handleInsertTable}>
                    <TableIcon className="mr-2 h-4 w-4" />
                    <span>Insert Table</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={handleInsertImage}>
                    <ImageIcon className="mr-2 h-4 w-4" />
                    <span>Insert Image</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => setLinkDialogOpen(true)}>
                    <LinkIcon className="mr-2 h-4 w-4" />
                    <span>Insert Link</span>
                  </ContextMenuItem>
                  {variables.length > 0 && (
                    <>
                      <ContextMenuSeparator />
                      <ContextMenuSub>
                        <ContextMenuSubTrigger>
                          <VariableIcon className="mr-2 h-4 w-4" />
                          <span>Insert Variable</span>
                        </ContextMenuSubTrigger>
                        <ContextMenuSubContent className="w-64">
                          <ScrollArea className="h-48">
                            {Object.entries(variablesByCategory).map(([category, vars]) => (
                              <div key={category}>
                                <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
                                  {categoryLabels[category as keyof typeof categoryLabels]}
                                </div>
                                {vars.map((variable) => (
                                  <ContextMenuItem
                                    key={variable.id}
                                    onClick={() => {
                                      handleInsertVariable(variable);
                                      handleCloseContextMenu();
                                    }}
                                  >
                                    <span className="truncate">{variable.label}</span>
                                  </ContextMenuItem>
                                ))}
                              </div>
                            ))}
                          </ScrollArea>
                        </ContextMenuSubContent>
                      </ContextMenuSub>
                    </>
                  )}
                  <ContextMenuSeparator />
                  <ContextMenuItem onClick={() => editor?.chain().focus().undo().run()} disabled={!editor?.can().undo()}>
                    <Undo className="mr-2 h-4 w-4" />
                    <span>Undo</span>
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+Z</span>
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => editor?.chain().focus().redo().run()} disabled={!editor?.can().redo()}>
                    <Redo className="mr-2 h-4 w-4" />
                    <span>Redo</span>
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+Y</span>
                  </ContextMenuItem>
                  </>
                )}
              </ContextMenuContent>
            </ContextMenu>
          </div>
        )}
      </div>


    </div>
  );
};

export default EnhancedRichTextEditor;
